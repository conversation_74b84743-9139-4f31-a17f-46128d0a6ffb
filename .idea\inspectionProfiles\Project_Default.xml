<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="monai" />
            <item index="1" class="java.lang.String" itemvalue="ipython" />
            <item index="2" class="java.lang.String" itemvalue="torch" />
            <item index="3" class="java.lang.String" itemvalue="torchvision" />
            <item index="4" class="java.lang.String" itemvalue="matplotlib" />
            <item index="5" class="java.lang.String" itemvalue="nnunetv2" />
            <item index="6" class="java.lang.String" itemvalue="PyQt5_sip" />
            <item index="7" class="java.lang.String" itemvalue="wandb" />
            <item index="8" class="java.lang.String" itemvalue="connected_components_3d" />
            <item index="9" class="java.lang.String" itemvalue="segmentation_models_pytorch" />
            <item index="10" class="java.lang.String" itemvalue="SimpleITK" />
            <item index="11" class="java.lang.String" itemvalue="pycocotools" />
            <item index="12" class="java.lang.String" itemvalue="skimage" />
            <item index="13" class="java.lang.String" itemvalue="ipywidgets" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>