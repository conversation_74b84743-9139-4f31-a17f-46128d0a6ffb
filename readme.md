# 智能建模平台

北京科技大学天码智能社

## 项目简介

智能建模平台是一个基于Streamlit开发的数学建模工具集，旨在为数学建模竞赛、科研分析和教学提供便捷的算法实现和可视化工具。本平台集成了多种常用的数学建模算法，包括数据预处理、多元回归、多准则决策分析、时间序列预测等功能模块，提供了友好的用户界面和交互式操作体验。

## 功能特点

- **多样化算法支持**：集成了8大类常用数学建模算法
- **交互式操作界面**：基于Streamlit构建的直观操作界面
- **数据可视化**：提供丰富的图表展示分析结果
- **示例数据生成**：内置示例数据生成功能，方便快速测试
- **文件导入导出**：支持Excel/CSV格式数据导入和结果导出
- **详细文档说明**：每个算法模块都提供了详细的参数说明和使用指南

## 算法体系

本平台包含以下算法模块：

1. **数据归一化/标准化**
   - 最小-最大归一化
   - Z-Score标准化
   - 最大绝对值归一化
   - 小数定标归一化
   - 向量归一化(L2范数)
   - 对数归一化
   - Softmax归一化

2. **多元回归/拟合**
   - 线性回归
   - 多项式回归
   - 岭回归
   - Lasso回归
   - 弹性网络回归
   - 支持向量回归

3. **多准则决策分析/评估**
   - 层次分析法(AHP)
   - TOPSIS法
   - 模糊综合评价法
   - 熵权法

4. **时间序列预测分析**
   - 移动平均法
   - 指数平滑法
   - ARIMA模型
   - Prophet模型

5. **数据挖掘分析**
   - K-means聚类
   - 层次聚类
   - DBSCAN聚类
   - 主成分分析(PCA)

6. **优化运筹**
   - 线性规划
   - 整数规划
   - 非线性规划

7. **启发式算法**
   - 遗传算法
   - 粒子群优化
   - 模拟退火算法

8. **数据预处理/异常值处理**
   - 3σ法则
   - 箱线图法
   - LOF局部异常因子
   - Isolation Forest孤立森林

## 使用指南

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动应用

```bash
streamlit run Home.py
```

### 数据准备

- 各算法页面均提供示例数据集
- 支持CSV/Excel格式文件上传
- 未上传数据时默认使用示例数据

### 操作提示

- 计算出结果后及时保存
- 部分算法源代码有参数讲解，如仍不理解请善用AI
- 支持一键导出处理结果（Excel格式）

### 注意事项

- Streamlit框架采用「单次点击单次执行」机制
- 切换页面后算法状态自动重置
- 复杂算法建议先用示例数据验证流程

## 项目结构

```
MathModeling_Streamlit/
├── Home.py                # 主页
├── pages/                 # 功能页面
│   ├── 1Normalize.py      # 数据归一化/标准化
│   ├── 2Regression.py     # 多元回归/拟合
│   ├── 3Evaluate.py       # 多准则决策分析/评估
│   ├── 4Time_Series.py    # 时间序列预测分析
│   ├── 5Data_Mining.py    # 数据挖掘分析
│   ├── 6Optimize.py       # 优化运筹
│   ├── 7Heuristic.py      # 启发式算法
│   └── 8Outlier.py        # 数据预处理/异常值处理
└── assets/                # 静态资源文件
```

## 贡献指南

欢迎对本项目提出改进建议或贡献代码。如果您有任何问题或建议，请通过Issues提交，或者直接提交Pull Request。

## 运行方式

1. 安装Python环境
2. 安装依赖：`pip install -r requirements.txt`
3. 运行应用：`streamlit run Home.py`